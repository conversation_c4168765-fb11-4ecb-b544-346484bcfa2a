#!/usr/bin/env python3
"""
Pure LangGraph SQL Agent with Robust Conversational Handling
"""

import asyncio
import os
import pyodbc
import httpx
from typing import TypedDict, Annotated, Literal, Dict, Any, Optional
from dotenv import load_dotenv
from openai import AzureOpenAI
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

load_dotenv()

# Database Configuration
CONNECTION_STRING = """
DRIVER={ODBC Driver 17 for SQL Server};
SERVER=**************;
DATABASE=GSC_Analytics;
Trusted_Connection=yes;
TrustServerCertificate=yes;
""".strip()

class AgentState(TypedDict):
    """LangGraph state definition with proper typing"""
    messages: Annotated[list, add_messages]
    user_input: str
    generated_sql: str
    sql_result: str
    error_message: str
    retry_count: int
    analysis: str
    execution_mode: str

class PureLangGraphSQLAgent:
    def __init__(self, safe_mode: bool = True, max_tokens: int = 1000):
        self.safe_mode = safe_mode
        self.max_tokens = max_tokens
        self.azure_client = None
        self.http_client = None
        self.app = None
        self.setup_azure_openai()
        self.setup_graph()

    def setup_azure_openai(self) -> None:
        """Setup Azure OpenAI client"""
        try:
            api_key = os.getenv("API_KEY")
            if not api_key:
                raise Exception("API_KEY not found")

            custom_cert_file = "pki-it-root 2.crt"
            if not os.path.exists(custom_cert_file):
                raise Exception(f"Certificate file {custom_cert_file} not found")

            self.http_client = httpx.Client(verify=custom_cert_file)
            self.azure_client = AzureOpenAI(
                azure_endpoint="https://apim-guardian-prv-fc.aihub.se.com",
                api_key=api_key,
                http_client=self.http_client,
                api_version="2024-02-01"
            )
            print("✅ Azure OpenAI initialized")
        except Exception as e:
            print(f"❌ Azure OpenAI setup failed: {str(e)}")
            raise e

    def setup_graph(self) -> None:
        """Setup LangGraph workflow with conversational handling"""
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("input_processor", self.process_input)
        workflow.add_node("sql_generator", self.generate_sql)
        workflow.add_node("sql_executor", self.execute_sql)
        workflow.add_node("error_handler", self.handle_error)
        workflow.add_node("result_analyzer", self.analyze_result)
        workflow.add_node("direct_executor", self.direct_sql_execution)
        workflow.add_node("conversational_responder", self.conversational_response)

        # Define the graph flow
        workflow.add_edge(START, "input_processor")
        workflow.add_conditional_edges(
            "input_processor",
            self.route_from_input,
            {
                "generate_sql": "sql_generator",
                "direct_sql": "direct_executor",
                "conversational": "conversational_responder",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "sql_generator",
            self.route_from_sql_generation,
            {
                "execute_sql": "sql_executor",
                "conversational": "conversational_responder",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "sql_executor",
            self.route_from_execution,
            {
                "success": "result_analyzer",
                "error": "error_handler",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "error_handler",
            self.route_from_error,
            {
                "retry": "sql_generator",
                "end": END
            }
        )
        
        workflow.add_edge("result_analyzer", END)
        workflow.add_edge("direct_executor", END)
        workflow.add_edge("conversational_responder", END)

        try:
            # Compile the graph
            self.app = workflow.compile()
            print("✅ LangGraph workflow compiled successfully with conversational support")
        except Exception as e:
            print(f"❌ Failed to compile LangGraph workflow: {str(e)}")
            self.app = None

    def is_conversational_query(self, user_input: str) -> bool:
        """Determine if query is conversational with comprehensive checks"""
        if not user_input or not user_input.strip():
            return True
        
        lower_input = user_input.lower().strip()
        
        # Exact match patterns
        exact_patterns = [
            "hi", "hello", "hey", "thanks", "thank you", "bye", "goodbye",
            "help", "capabilities", "who are you", "what can you do",
            "what do you do", "how are you", "what's up"
        ]
        
        # Check for exact matches
        if lower_input in exact_patterns:
            return True
        
        # Check for patterns within the input
        conversational_patterns = [
            "hi ", " hi", "hello ", " hello", "hey ", " hey",
            "good morning", "good afternoon", "good evening", 
            "how are you", "what's up", "thanks", "thank you",
            "who are you", "what can you do", "capabilities",
            "what do you do", "introduce yourself", "about you"
        ]
        
        for pattern in conversational_patterns:
            if pattern in lower_input:
                return True
        
        # Check for short queries that are likely conversational
        words = user_input.split()
        if len(words) <= 3:
            conversational_words = ["hi", "hello", "hey", "help", "thanks", "bye"]
            if any(word.lower() in conversational_words for word in words):
                return True
        
        # Check if it doesn't contain data-related keywords
        data_keywords = [
            "show", "display", "find", "get", "list", "count", "sum", "average",
            "total", "how many", "what is", "which", "where", "when", "who",
            "order", "supplier", "region", "product", "quantity", "priority",
            "delivery", "transport", "table", "database", "select", "from",
            "orders", "data", "records", "rows", "sql"
        ]
        
        has_data_keywords = any(keyword in lower_input for keyword in data_keywords)
        
        # If no data keywords and short query, likely conversational
        if not has_data_keywords and len(words) <= 5:
            return True
        
        return False

    def process_input(self, state: AgentState) -> AgentState:
        """Process and categorize user input with robust classification"""
        user_input = state.get("user_input", "").strip()
        print(f"🔧 Processing input: '{user_input}'")
        
        if not user_input:
            state["execution_mode"] = "conversational"
            state["generated_sql"] = ""
            print(f"🎯 Empty input - classified as: conversational")
            return state
        
        lower_input = user_input.lower().strip()
        
        # PRIORITY 1: Direct SQL commands
        if lower_input.startswith('direct:'):
            state["execution_mode"] = "direct"
            sql_query = user_input.split(':', 1)[1].strip()
            state["generated_sql"] = sql_query
            state["retry_count"] = 0
            state["error_message"] = ""
            print(f"🎯 Classified as: direct SQL")
            return state
        
        if lower_input.startswith('convert:'):
            state["execution_mode"] = "convert"
            state["retry_count"] = 0
            state["error_message"] = ""
            print(f"🎯 Classified as: convert")
            return state
        
        # PRIORITY 2: Conversational check (MOST IMPORTANT)
        if self.is_conversational_query(user_input):
            state["execution_mode"] = "conversational"
            state["generated_sql"] = ""
            state["retry_count"] = 0
            state["error_message"] = ""
            print(f"🎯 Classified as: conversational")
            return state
        
        # PRIORITY 3: Data queries
        state["execution_mode"] = "agent"
        state["retry_count"] = 0
        state["error_message"] = ""
        print(f"🎯 Classified as: agent (data query)")
        return state

    def conversational_response(self, state: AgentState) -> AgentState:
        """Handle conversational queries without SQL execution"""
        user_input = state["user_input"]
        print(f"💬 CONVERSATIONAL HANDLER - Processing: '{user_input}'")
        
        try:
            lower_input = user_input.lower().strip()
            
            # Predefined responses for common patterns
            if any(greeting in lower_input for greeting in ["hi", "hello", "hey"]):
                response = "Hello! I'm your SQL Agent assistant. I can help you analyze data from the Orders_Test_AgenticAI database. You can ask me questions about orders, suppliers, regions, products, and more. What would you like to know?"
            
            elif any(cap in lower_input for cap in ["capabilities", "what can you do", "what do you do"]):
                response = """I'm a SQL Agent that can help you analyze the Orders_Test_AgenticAI database. Here's what I can do:

**Data Analysis**: Query orders, suppliers, regions, products, quantities
**Search & Filter**: Find specific orders by criteria (region, priority, etc.)
**Analytics**: Count orders, calculate totals, analyze trends
**Natural Language**: Just ask questions in plain English!

**Examples:**
- "Show me top 5 orders"
- "How many high priority orders from Europe?"
- "Orders delivered by sea transport"
- "Count orders per supplier"

What would you like to explore?"""
            
            elif "help" in lower_input:
                response = """I can help you query the Orders_Test_AgenticAI database! Here are some ways to interact:

**Natural Language**: Ask questions in plain English
**Direct SQL**: Use "direct: SELECT ..." for custom SQL
**Examples**: Try the example queries in the sidebar

**Sample Questions:**
- "Show me recent orders"
- "Which suppliers have the most orders?"
- "Orders from Asia with high priority"

What data would you like to see?"""
            
            elif any(thanks in lower_input for thanks in ["thanks", "thank you"]):
                response = "You're welcome! Feel free to ask me any questions about the Orders_Test_AgenticAI database. I'm here to help!"
            
            elif any(bye in lower_input for bye in ["bye", "goodbye"]):
                response = "Goodbye! Come back anytime you need help with database queries. Have a great day!"
            
            else:
                # Fallback response
                response = "I'm here to help with your database queries. You can ask me about orders, suppliers, regions, products, and more from the Orders_Test_AgenticAI database. What would you like to know?"
            
            state["sql_result"] = response
            state["analysis"] = ""
            state["generated_sql"] = ""
            state["error_message"] = ""
            
            print(f"💬 CONVERSATIONAL RESPONSE: Generated response successfully")
            
        except Exception as e:
            print(f"⚠️ Conversational response failed: {str(e)}")
            state["sql_result"] = "Hello! I'm your SQL Agent assistant. I can help you query the Orders_Test_AgenticAI database. What would you like to know about the data?"
            state["analysis"] = ""
            state["generated_sql"] = ""
            state["error_message"] = ""
        
        return state

    def generate_sql(self, state: AgentState) -> AgentState:
        """Generate SQL using Azure OpenAI with enhanced rules"""
        user_input = state["user_input"]
        retry_count = state.get("retry_count", 0)
        
        print(f"🤖 SQL GENERATOR CALLED - Input: '{user_input}', Mode: '{state.get('execution_mode')}'")
        
        # SAFETY CHECK: If this is conversational, don't generate SQL
        if state.get("execution_mode") == "conversational":
            print("🛑 SQL Generator received conversational query - redirecting")
            return state
        
        # Additional safety check for obvious conversational patterns
        if self.is_conversational_query(user_input):
            print("🛑 SQL Generator detected conversational query - redirecting")
            state["execution_mode"] = "conversational"
            state["generated_sql"] = ""
            return state
        
        print(f"🤖 Proceeding with SQL generation (attempt {retry_count + 1})")

        try:
            system_prompt = """You are an expert SQL generator for the Orders_Test_AgenticAI table.

DATABASE SCHEMA:
Table: Orders_Test_AgenticAI (50,001 records)
- OrderID (Primary Key): Format ORD-10000, ORD-10001, etc.
- OrderDate: Date when order was placed
- DeliveryDate: Expected delivery date
- Supplier: Supplier name
- Region: Geographic region (Asia, Europe, North America, South America)
- ProductType: Type of product (Circuit Breaker, Relay, Motor Starter, Sensor, HMI, PLC)
- Quantity: Number of items (integer)
- ModeOfTransport: Shipping method (Sea, Rail, Road, Air)
- Priority: Order priority (Low, Medium, High)

RULES FOR DATA QUERIES:
1. Generate valid SQL Server syntax only
2. Use TOP clause for limits (e.g., TOP 5, TOP 10)
3. Always include ORDER BY for consistent results
4. Use proper WHERE clauses for filtering
5. For aggregations, use GROUP BY appropriately

Generate ONLY the SQL query, no explanations."""

            if state.get("error_message"):
                system_prompt += f"\n\nPREVIOUS ERROR: {state['error_message'][:100]}\nFix the SQL syntax."

            if self.azure_client is None:
                state["error_message"] = "Azure OpenAI client not initialized"
                return state

            response = self.azure_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_input}
                ],
                max_tokens=min(self.max_tokens, 300),
                temperature=0.1
            )

            if response and response.choices and len(response.choices) > 0:
                message_content = response.choices[0].message.content
                if message_content is not None:
                    sql_query = message_content.strip()
                else:
                    sql_query = ""
                    state["error_message"] = "Failed to generate SQL: Received empty content"
            else:
                sql_query = ""
                state["error_message"] = "Failed to generate SQL: Empty response"

            # Clean up the SQL
            if sql_query.startswith("```sql"):
                sql_query = sql_query.replace("```sql", "").replace("```", "")
            elif sql_query.startswith("```"):
                sql_query = sql_query.replace("```", "")

            state["generated_sql"] = sql_query
            print(f"🤖 Generated SQL: {sql_query}")

        except Exception as e:
            state["error_message"] = f"SQL generation failed: {str(e)}"
            print(f"❌ SQL generation error: {str(e)}")

        return state

    def execute_sql(self, state: AgentState) -> AgentState:
        """Execute SQL query against database with optimized results"""
        sql_query = state["generated_sql"]
        print(f"🔧 Executing SQL: {sql_query}")

        # Safety check
        if self.safe_mode:
            dangerous_operations = [
                'DROP DATABASE', 'TRUNCATE TABLE', 'DELETE FROM Orders_Test_AgenticAI WHERE 1=1',
                'DROP TABLE Orders_Test_AgenticAI', 'ALTER TABLE Orders_Test_AgenticAI DROP'
            ]
            query_upper = sql_query.upper().strip()
            for dangerous in dangerous_operations:
                if dangerous in query_upper:
                    state["error_message"] = f"🛡️ SAFETY CHECK: Query blocked - contains dangerous operation: {dangerous}"
                    return state

        try:
            conn = pyodbc.connect(CONNECTION_STRING, timeout=15)
            cursor = conn.cursor()
            cursor.execute(sql_query)

            # Determine if it's a SELECT query
            is_select = sql_query.strip().upper().startswith(('SELECT', 'WITH', 'SHOW'))

            if is_select:
                rows = cursor.fetchall()
                columns = [desc for desc in cursor.description] if cursor.description else []

                if not rows:
                    state["sql_result"] = f"Query: {sql_query}\nNo data found."
                else:
                    # OPTIMIZED: Limit result size to save tokens
                    max_rows_display = 10
                    max_col_width = 12
                    
                    result = f"Query: {sql_query}\n"
                    result += f"Total Rows: {len(rows)} (showing first {min(len(rows), max_rows_display)})\n"
                    result += "-" * 60 + "\n"
                    
                    # Compact header
                    header = " | ".join(f"{col[:max_col_width]:<{max_col_width}}" for col in columns)
                    result += header + "\n"
                    result += "-" * len(header) + "\n"
                    
                    # Compact data rows
                    for i, row in enumerate(rows[:max_rows_display]):
                        row_data = []
                        for cell in row:
                            if cell is None:
                                row_data.append("NULL".ljust(max_col_width))
                            else:
                                cell_str = str(cell)[:max_col_width]
                                row_data.append(cell_str.ljust(max_col_width))
                        result += " | ".join(row_data) + "\n"
                    
                    state["sql_result"] = result
            else:
                # Handle UPDATE/INSERT/DELETE
                affected_rows = cursor.rowcount
                conn.commit()
                state["sql_result"] = f"✅ Query executed successfully\nSQL: {sql_query}\nRows affected: {affected_rows}"

            conn.close()
            print("✅ SQL execution successful")

        except Exception as e:
            state["error_message"] = f"Database execution error: {str(e)}"
            print(f"❌ SQL execution error: {str(e)}")

        return state

    def handle_error(self, state: AgentState) -> AgentState:
        """Handle execution errors with retry logic"""
        error_msg = state["error_message"]
        retry_count = state.get("retry_count", 0)
        print(f"⚠️ Handling error (attempt {retry_count + 1}): {error_msg}")
        
        state["retry_count"] = retry_count + 1
        
        if retry_count >= 2:
            state["sql_result"] = f"❌ Failed after {retry_count + 1} attempts. Final error: {error_msg}"
        
        return state

    def analyze_result(self, state: AgentState) -> AgentState:
        """Analyze SQL results with token optimization"""
        sql_result = state["sql_result"]
        user_input = state["user_input"]
        
        # OPTIMIZED: Only analyze complex results or when explicitly requested
        should_analyze = (
            len(sql_result) > 500 and
            ("analyze" in user_input.lower() or 
             "explain" in user_input.lower() or 
             "insights" in user_input.lower() or
             "summary" in user_input.lower())
        )
        
        if not should_analyze:
            state["analysis"] = ""
            return state
        
        print("🧠 Analyzing results with AI...")
        
        try:
            if self.azure_client is not None:
                # OPTIMIZED: Shorter analysis prompt and truncated input
                truncated_result = sql_result[:800]
                
                response = self.azure_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {
                            "role": "system",
                            "content": "Provide brief insights from SQL results. 2-3 sentences max."
                        },
                        {
                            "role": "user",
                            "content": f"Query: {user_input[:100]}\nResults: {truncated_result}\nKey insights:"
                        }
                    ],
                    max_tokens=150,
                    temperature=0.3
                )
                
                if response and response.choices and len(response.choices) > 0:
                    message_content = response.choices[0].message.content
                    if message_content is not None:
                        analysis = message_content.strip()
                        state["analysis"] = f"\n🧠 **Insights:** {analysis}"
                    else:
                        state["analysis"] = ""
                else:
                    state["analysis"] = ""
        except Exception as e:
            print(f"⚠️ Analysis failed: {str(e)}")
            state["analysis"] = ""
        
        return state

    def direct_sql_execution(self, state: AgentState) -> AgentState:
        """Direct SQL execution for 'direct:' commands"""
        sql_query = state["generated_sql"]
        print(f"🔧 Direct SQL execution: {sql_query}")
        
        temp_state: AgentState = {
            "messages": [],
            "user_input": state["user_input"],
            "generated_sql": sql_query,
            "sql_result": "",
            "error_message": "",
            "retry_count": 0,
            "analysis": "",
            "execution_mode": "direct"
        }
        
        temp_result = self.execute_sql(temp_state)
        state["sql_result"] = temp_result.get("sql_result", "")
        state["error_message"] = temp_result.get("error_message", "")
        state["analysis"] = ""
        
        return state

    # ROUTING FUNCTIONS
    def route_from_input(self, state: AgentState) -> Literal["generate_sql", "direct_sql", "conversational", "end"]:
        """Route based on input processing with debugging"""
        mode = state["execution_mode"]
        print(f"🔀 route_from_input: mode = '{mode}'")
        
        if mode == "direct":
            print("🔀 → Routing to direct_sql")
            return "direct_sql"
        elif mode == "conversational":
            print("🔀 → Routing to conversational")
            return "conversational"
        elif mode in ["agent", "convert"]:
            print("🔀 → Routing to generate_sql")
            return "generate_sql"
        else:
            print(f"🔀 → Routing to end (unknown mode: {mode})")
            return "end"

    def route_from_sql_generation(self, state: AgentState) -> Literal["execute_sql", "conversational", "end"]:
        """Route after SQL generation to handle non-data queries"""
        mode = state.get("execution_mode", "unknown")
        sql = state.get("generated_sql", "")
        error = state.get("error_message", "")
        
        print(f"🔀 route_from_sql_generation: mode='{mode}', has_sql={bool(sql)}, has_error={bool(error)}")
        
        if mode == "conversational":
            print("🔀 → Routing to conversational")
            return "conversational"
        elif sql and not error:
            print("🔀 → Routing to execute_sql")
            return "execute_sql"
        else:
            print("🔀 → Routing to end")
            return "end"

    def route_from_execution(self, state: AgentState) -> Literal["success", "error", "end"]:
        """Route based on SQL execution result"""
        if state.get("error_message"):
            return "error"
        elif state.get("sql_result"):
            return "success"
        else:
            return "end"

    def route_from_error(self, state: AgentState) -> Literal["retry", "end"]:
        """Route based on error handling"""
        retry_count = state.get("retry_count", 0)
        if retry_count < 3 and not state["error_message"].startswith("🛡️"):
            return "retry"
        else:
            return "end"

    def test_connection(self) -> str:
        """Test database connection"""
        try:
            print(f"🔧 Testing connection with: {CONNECTION_STRING}")
            conn = pyodbc.connect(CONNECTION_STRING, timeout=10)
            cursor = conn.cursor()
            cursor.execute("SELECT GETDATE() as CurrentTime, COUNT(*) as TotalRecords FROM Orders_Test_AgenticAI")
            result = cursor.fetchone()
            conn.close()
            
            if result is None:
                return "✅ Connection Successful, but no data returned from test query"
            
            return f"✅ Connection Test Successful\nCurrent Time: {result}\nTotal Records: {result[1]}"
        except Exception as e:
            return f"❌ Connection Test Failed: {str(e)}"

    # Context support methods for conversation continuity
    def optimize_conversation_context(self, chat_history: list, max_pairs: int = 2) -> list:
        """Optimize conversation context to reduce tokens"""
        if not chat_history:
            return []
        
        # Keep only last N Q&A pairs
        relevant_messages = []
        user_count = 0
        
        # Process from newest to oldest
        for msg in reversed(chat_history):
            if msg["role"] == "user":
                user_count += 1
                if user_count <= max_pairs:
                    # Truncate long queries
                    content = msg["content"][:200] + "..." if len(msg["content"]) > 200 else msg["content"]
                    relevant_messages.insert(0, {"role": "user", "content": content})
            elif msg["role"] == "assistant" and user_count <= max_pairs:
                # Only include SQL and brief result summary
                content = msg.get("content", {})
                if isinstance(content, dict):
                    sql = content.get("sql", "")[:150]  # Truncate SQL
                    summary = f"SQL: {sql}...\nResult: Data retrieved successfully"
                    relevant_messages.insert(0, {"role": "assistant", "content": summary})
                else:
                    # Handle string content
                    summary = str(content)[:150] + "..." if len(str(content)) > 150 else str(content)
                    relevant_messages.insert(0, {"role": "assistant", "content": summary})
        
        return relevant_messages
    async def process_query_with_context(self, query: str, chat_history: Optional[list] = None) -> Dict[str, Any]:
        """Process query with optimized context"""
        if chat_history is None:
            chat_history = []
        
        # OPTIMIZED: Use only essential context
        context_messages = self.optimize_conversation_context(chat_history, max_pairs=2)
        
        initial_state: AgentState = {
            "messages": context_messages,
            "user_input": query,
            "generated_sql": "",
            "sql_result": "",
            "error_message": "",
            "retry_count": 0,
            "analysis": "",
            "execution_mode": ""
        }
        
        try:
            if self.app is None:
                return {"error_message": "LangGraph workflow not initialized properly"}
            final_state = await self.app.ainvoke(initial_state)
            return final_state
        except Exception as e:
            return {"error_message": f"Processing failed: {str(e)}"}

    async def chat_loop(self) -> None:
        """Interactive chat loop"""
        print("\n" + "="*60)
        print("🗣️ PURE LANGGRAPH SQL AGENT WITH CONVERSATIONAL SUPPORT")
        print("="*60)
        print("Features:")
        print("- Pure LangGraph architecture")
        print("- Robust conversational query handling")
        print("- Intelligent SQL generation with retry logic")
        print("- AI-powered result analysis")
        print("- Token optimization")
        print("- Azure OpenAI integration")
        
        print("\nCommands:")
        print("- Hi (conversational response)")
        print("- Show me the top 5 rows from Orders_Test_AgenticAI")
        print("- Display all high priority orders from Europe")
        print("- How many orders per supplier?")
        print("- direct: SELECT * FROM Orders_Test_AgenticAI WHERE Region = 'Asia'")
        print("- test")
        
        print("\nType 'quit' to exit")
        print("-"*60)

        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break

                if not user_input:
                    continue

                # Special commands
                if user_input.lower() == 'test':
                    result = self.test_connection()
                    print(f"📊 Connection Test:\n{result}")
                    continue

                # Process through LangGraph
                print("🤖 Processing through LangGraph...")
                initial_state: AgentState = {
                    "messages": [],
                    "user_input": user_input,
                    "generated_sql": "",
                    "sql_result": "",
                    "error_message": "",
                    "retry_count": 0,
                    "analysis": "",
                    "execution_mode": ""
                }

                # Run the graph
                if self.app is not None:
                    try:
                        final_state = await self.app.ainvoke(initial_state)
                    except AttributeError as e:
                        print(f"❌ Error: Graph execution failed - {str(e)}")
                        print("Attempting to reinitialize the graph...")
                        self.setup_graph()
                        if self.app is not None:
                            final_state = await self.app.ainvoke(initial_state)
                        else:
                            print("❌ Failed to reinitialize the graph")
                            continue

                # Run the graph
                if self.app is not None:
                    final_state = await self.app.ainvoke(initial_state)

                    # Display results
                    if final_state.get("sql_result"):
                        print(f"📊 **Agent Result:**\n")
                        print(final_state["sql_result"])
                        if final_state.get("analysis"):
                            print(final_state["analysis"])
                    else:
                        print("❌ No result generated")

                    # Show execution info
                    if final_state.get("retry_count", 0) > 0:
                        print(f"\n🔄 Completed after {final_state['retry_count']} retries")
                else:
                    print("❌ Graph not initialized")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")

async def main() -> None:
    """Main function"""
    print("🚀 Pure LangGraph SQL Agent with Robust Conversational Support")
    
    if not os.getenv("API_KEY"):
        print("❌ Error: API_KEY not found in environment")
        return

    print("\nChoose mode:")
    print("1. 🛡️ Safe Mode")
    print("2. ⚠️ Unrestricted Mode")
    choice = input("Select (1 or 2): ").strip()
    safe_mode = choice != "2"

    try:
        agent = PureLangGraphSQLAgent(safe_mode=safe_mode)
        await agent.chat_loop()
    except Exception as e:
        print(f"❌ Initialization failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
