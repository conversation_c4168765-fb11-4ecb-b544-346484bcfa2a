import streamlit as st
import asyncio
import pandas as pd
from typing import Dict, Any
import json
import os
from datetime import datetime
from client import PureLangGraphSQLAgent, AgentState

# Configure page
st.set_page_config(
    page_title="SQL Agent Chat",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for hover effects
st.markdown("""
<style>
.chat-item {
    position: relative;
    padding: 8px;
    border-radius: 8px;
    margin-bottom: 4px;
    transition: background-color 0.2s;
}

.chat-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.delete-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.2s;
    background: rgba(255, 0, 0, 0.8);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
}

.chat-item:hover .delete-btn {
    opacity: 1;
}

.delete-btn:hover {
    background: rgba(255, 0, 0, 1);
}

.chat-name {
    font-weight: bold;
    margin-bottom: 2px;
}

.chat-timestamp {
    font-size: 11px;
    color: #888;
    margin-top: 2px;
}
</style>
""", unsafe_allow_html=True)

# Chat history file path
CHAT_HISTORY_DIR = "chat_history"
if not os.path.exists(CHAT_HISTORY_DIR):
    os.makedirs(CHAT_HISTORY_DIR)

def generate_chat_name(first_message: str) -> str:
    """Generate a ChatGPT-style chat name from the first message"""
    if not first_message:
        return "New Chat"
    
    # Clean the message
    clean_message = first_message.strip()
    
    # Remove direct SQL prefixes
    if clean_message.lower().startswith("direct:"):
        clean_message = clean_message[7:].strip()
    elif clean_message.lower().startswith("convert:"):
        clean_message = clean_message[8:].strip()
    
    # Truncate and create meaningful name
    if len(clean_message) <= 50:
        return clean_message
    
    # Find natural break points
    words = clean_message.split()
    name = ""
    for word in words:
        if len(name + " " + word) <= 45:
            name += " " + word if name else word
        else:
            break
    
    return name + "..." if name else clean_message[:45] + "..."

def get_relative_time(timestamp_str: str) -> str:
    """Get relative time like 'Last message 11 hours ago'"""
    try:
        timestamp = datetime.fromisoformat(timestamp_str)
        now = datetime.now()
        diff = now - timestamp
        
        if diff.days > 0:
            return f"Last message {diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds >= 3600:
            hours = diff.seconds // 3600
            return f"Last message {hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds >= 60:
            minutes = diff.seconds // 60
            return f"Last message {minutes} minute{'s' if minutes > 1 else ''} ago"
        else:
            return "Last message just now"
    except:
        return "Last message recently"

def auto_save_chat():
    """Automatically save current chat"""
    if st.session_state.chat_history:
        save_chat_history(st.session_state.current_session_id, st.session_state.chat_history)

def save_chat_history(session_id: str, messages: list):
    """Save chat history to file with enhanced metadata"""
    try:
        # Generate chat name from first user message
        chat_name = "New Chat"
        if messages:
            for msg in messages:
                if msg["role"] == "user":
                    chat_name = generate_chat_name(msg["content"])
                    break
        
        filepath = os.path.join(CHAT_HISTORY_DIR, f"{session_id}.json")
        with open(filepath, 'w') as f:
            json.dump({
                "session_id": session_id,
                "chat_name": chat_name,
                "timestamp": datetime.now().isoformat(),
                "messages": messages
            }, f, indent=2)
    except Exception as e:
        st.error(f"Failed to save chat history: {str(e)}")

def load_chat_history(session_id: str) -> list:
    """Load chat history from file"""
    try:
        filepath = os.path.join(CHAT_HISTORY_DIR, f"{session_id}.json")
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                data = json.load(f)
                return data.get("messages", [])
    except Exception as e:
        st.error(f"Failed to load chat history: {str(e)}")
    return []

def get_all_chat_sessions() -> list:
    """Get all available chat sessions with enhanced metadata"""
    sessions = []
    try:
        for filename in os.listdir(CHAT_HISTORY_DIR):
            if filename.endswith('.json'):
                filepath = os.path.join(CHAT_HISTORY_DIR, filename)
                with open(filepath, 'r') as f:
                    data = json.load(f)
                    
                    # Get chat name (generate if not exists)
                    chat_name = data.get("chat_name", "")
                    if not chat_name and data.get("messages"):
                        for msg in data["messages"]:
                            if msg["role"] == "user":
                                chat_name = generate_chat_name(msg["content"])
                                break
                    
                    sessions.append({
                        "id": data["session_id"],
                        "name": chat_name or "New Chat",
                        "timestamp": data["timestamp"],
                        "message_count": len(data.get("messages", [])),
                        "relative_time": get_relative_time(data["timestamp"])
                    })
        
        # Sort by timestamp (newest first)
        sessions.sort(key=lambda x: x["timestamp"], reverse=True)
    except Exception as e:
        st.error(f"Failed to load chat sessions: {str(e)}")
    return sessions

def delete_chat_session(session_id: str):
    """Delete a chat session"""
    try:
        filepath = os.path.join(CHAT_HISTORY_DIR, f"{session_id}.json")
        if os.path.exists(filepath):
            os.remove(filepath)
            return True
    except Exception as e:
        st.error(f"Failed to delete chat session: {str(e)}")
    return False

def generate_session_id() -> str:
    """Generate a new session ID"""
    return f"chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

# Initialize session state
if 'agent' not in st.session_state:
    st.session_state.agent = None
if 'current_session_id' not in st.session_state:
    st.session_state.current_session_id = generate_session_id()
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = load_chat_history(st.session_state.current_session_id)
if 'connection_status' not in st.session_state:
    st.session_state.connection_status = None
if 'delete_confirmation' not in st.session_state:
    st.session_state.delete_confirmation = None

# Auto-save when session state changes
if 'last_message_count' not in st.session_state:
    st.session_state.last_message_count = len(st.session_state.chat_history)

# Detect if new messages were added and auto-save
current_message_count = len(st.session_state.chat_history)
if current_message_count != st.session_state.last_message_count:
    auto_save_chat()
    st.session_state.last_message_count = current_message_count

@st.cache_resource
def initialize_agent(safe_mode: bool = True):
    """Initialize the SQL agent with caching"""
    try:
        agent = PureLangGraphSQLAgent(safe_mode=safe_mode)
        return agent, "Agent initialized successfully"
    except Exception as e:
        return None, f"Failed to initialize agent: {str(e)}"

def format_sql_result(result: str) -> Dict[str, Any]:
    """Parse and format SQL results for better display"""
    if "No data found" in result:
        return {"type": "no_data", "message": result}
    
    if "Query executed successfully" in result:
        return {"type": "success", "message": result}
    
    if "❌" in result or "Error" in result:
        return {"type": "error", "message": result}
    
    # Try to extract tabular data
    lines = result.split('\n')
    if len(lines) > 4 and '|' in result:
        # Find header and data rows
        header_idx = None
        for i, line in enumerate(lines):
            if '|' in line and not line.startswith('-'):
                header_idx = i
                break
        
        if header_idx:
            headers = [col.strip() for col in lines[header_idx].split('|')]
            data_rows = []
            for line in lines[header_idx + 2:]:  # Skip separator line
                if line.strip() and '|' in line and not line.startswith('...'):
                    row = [col.strip() for col in line.split('|')]
                    if len(row) == len(headers):
                        data_rows.append(row)
            
            if data_rows:
                return {
                    "type": "table",
                    "headers": headers,
                    "data": data_rows,
                    "raw": result
                }
    
    return {"type": "text", "message": result}

async def process_query(query: str, agent: PureLangGraphSQLAgent) -> Dict[str, Any]:
    """Process query through the agent"""
    initial_state = {
        "messages": [],
        "user_input": query,
        "generated_sql": "",
        "sql_result": "",
        "error_message": "",
        "retry_count": 0,
        "analysis": "",
        "execution_mode": ""
    }
    
    try:
        if agent is None or agent.app is None:
            return {"error_message": "Agent not properly initialized. Please reinitialize the agent."}
            
        final_state = await agent.app.ainvoke(initial_state)
        return final_state
    except Exception as e:
        return {"error_message": f"Processing failed: {str(e)}"}

# Sidebar Configuration
with st.sidebar:
    st.title("SQL Agent Settings")
    
    # Chat Management Section
    st.subheader("Chat Management")
    
    # New Chat Button
    if st.button("Start New Chat", type="primary", use_container_width=True):
        # Auto-save current chat before starting new one
        auto_save_chat()
        
        # Start new session
        st.session_state.current_session_id = generate_session_id()
        st.session_state.chat_history = []
        st.session_state.delete_confirmation = None
        st.session_state.last_message_count = 0
        st.rerun()
    
    # Chat History Management
    with st.expander("Chat History", expanded=False):
        sessions = get_all_chat_sessions()
        
        if sessions:
            st.write("**Previous Chats:**")
            
            # Handle delete confirmation
            if st.session_state.delete_confirmation:
                st.warning(f"Delete chat: {st.session_state.delete_confirmation}?")
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Yes, Delete", key="confirm_delete", type="primary"):
                        if delete_chat_session(st.session_state.delete_confirmation):
                            st.success("Chat deleted!")
                            # If deleted session is current session, start new one
                            if st.session_state.delete_confirmation == st.session_state.current_session_id:
                                st.session_state.current_session_id = generate_session_id()
                                st.session_state.chat_history = []
                                st.session_state.last_message_count = 0
                        st.session_state.delete_confirmation = None
                        st.rerun()
                with col2:
                    if st.button("Cancel", key="cancel_delete"):
                        st.session_state.delete_confirmation = None
                        st.rerun()
            
            else:
                # Display enhanced chat sessions
                for i, session in enumerate(sessions[:15]):  # Show last 15 sessions
                    # Create chat item with hover effects
                    chat_html = f"""
                    <div class="chat-item" id="chat-{i}">
                        <div class="chat-name">{session['name']}</div>
                        <div class="chat-timestamp">{session['relative_time']}</div>
                    </div>
                    """
                    
                    col1, col2 = st.columns([5, 1])
                    
                    with col1:
                        # Chat session button
                        if st.button(
                            f"{session['name']}\n{session['relative_time']}",
                            key=f"load_{session['id']}_{i}",
                            use_container_width=True,
                            help=f"Load chat: {session['name']}"
                        ):
                            # Auto-save current chat first
                            auto_save_chat()
                            
                            # Load selected chat
                            st.session_state.current_session_id = session['id']
                            st.session_state.chat_history = load_chat_history(session['id'])
                            st.session_state.delete_confirmation = None
                            st.session_state.last_message_count = len(st.session_state.chat_history)
                            st.rerun()
                    
                    with col2:
                        # Delete button (always visible but styled for hover effect)
                        if st.button("🗑", key=f"delete_{session['id']}_{i}", help="Delete"):
                            st.session_state.delete_confirmation = session['id']
                            st.rerun()
                    
                    # Add divider between sessions
                    if i < len(sessions) - 1 and i < 14:
                        st.divider()
            
            # Bulk actions (only Delete All, removed Save All)
            st.subheader("Bulk Actions")
            if st.button("Delete All", help="Delete all chat history", use_container_width=True):
                if st.session_state.delete_confirmation != "ALL_CHATS":
                    st.session_state.delete_confirmation = "ALL_CHATS"
                    st.rerun()
            
            # Confirm delete all
            if st.session_state.delete_confirmation == "ALL_CHATS":
                st.error("Delete ALL chat history? This cannot be undone!")
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Yes, Delete All", key="confirm_delete_all", type="primary"):
                        try:
                            for filename in os.listdir(CHAT_HISTORY_DIR):
                                if filename.endswith('.json'):
                                    os.remove(os.path.join(CHAT_HISTORY_DIR, filename))
                            st.session_state.current_session_id = generate_session_id()
                            st.session_state.chat_history = []
                            st.session_state.delete_confirmation = None
                            st.session_state.last_message_count = 0
                            st.success("All chats deleted!")
                            st.rerun()
                        except Exception as e:
                            st.error(f"Failed to delete all chats: {str(e)}")
                with col2:
                    if st.button("Cancel", key="cancel_delete_all"):
                        st.session_state.delete_confirmation = None
                        st.rerun()
        
        else:
            st.info("No previous chats found")
    
    st.divider()
    
    # Agent initialization
    st.subheader("Agent Configuration")
    safe_mode = st.toggle("Safe Mode", value=True, help="Prevents dangerous SQL operations")
    
    if st.button("Initialize Agent", use_container_width=True):
        with st.spinner("Initializing agent..."):
            agent, status = initialize_agent(safe_mode)
            st.session_state.agent = agent
            st.session_state.connection_status = status
    
    # Connection status
    if st.session_state.connection_status:
        if "successfully" in st.session_state.connection_status:
            st.success(st.session_state.connection_status)
        else:
            st.error(st.session_state.connection_status)
    
    # Test connection
    if st.session_state.agent and st.button("Test Database Connection"):
        with st.spinner("Testing connection..."):
            result = st.session_state.agent.test_connection()
            if "successfully" in result or "Connection" in result:
                st.success(result)
            else:
                st.error(result)
    
    st.divider()
    
    # Query examples
    st.subheader("Example Queries")
    examples = [
        "Show me the top 5 orders",
        "Display all high priority orders from Europe", 
        "How many orders per supplier?",
        "Orders delivered by sea transport",
        "direct: SELECT COUNT(*) FROM Orders_Test_AgenticAI"
    ]
    
    for example in examples:
        if st.button(example, key=f"example_{hash(example)}", use_container_width=True):
            st.session_state.example_query = example

# Main Interface
st.title("SQL Agent Chat Interface")
st.markdown("Chat with your database using natural language or direct SQL commands")

# Query modes info (removed Save Chat and Clear Current buttons)
col1, col2, col3 = st.columns(3)
with col1:
    st.info("**Natural Language**: Ask questions in plain English")
with col2:
    st.info("**Direct SQL**: Prefix with `direct:` for raw SQL")
with col3:
    st.info("**Convert**: Prefix with `convert:` for query conversion")

st.divider()

# Chat Interface
chat_container = st.container()

# Display chat history
with chat_container:
    for i, message in enumerate(st.session_state.chat_history):
        if message["role"] == "user":
            with st.chat_message("user"):
                st.write(message["content"])
        else:
            with st.chat_message("assistant"):
                result_data = message["content"]
            
                # Display results based on type
                formatted_result = format_sql_result(result_data.get("result", ""))
                
                # Display generated SQL if available (but not for no_data results)
                if "sql" in result_data and formatted_result["type"] != "no_data":
                    sql_query = result_data["sql"]
                    if sql_query.strip():
                        st.markdown(
                            f"""
                            ```
                            {sql_query}
                            ```
                            """
                        )
                
                if formatted_result["type"] == "table":
                    st.success("Query executed successfully!")
                    df = pd.DataFrame(formatted_result["data"], columns=formatted_result["headers"])
                    st.dataframe(df, use_container_width=True)
                    
                    with st.expander("View Raw Results"):
                        st.text(formatted_result["raw"])
                        
                elif formatted_result["type"] == "error":
                    st.error(formatted_result["message"])
                    
                elif formatted_result["type"] == "success":
                    st.success(formatted_result["message"])
                    
                else:
                    st.write(formatted_result["message"])
                
                # Display AI analysis if available
                if result_data.get("analysis"):
                    st.markdown(result_data["analysis"])
                
                # Show retry information
                if result_data.get("retry_count", 0) > 0:
                    st.info(f"Completed after {result_data['retry_count']} retries")

# Handle example queries from sidebar
if hasattr(st.session_state, 'example_query'):
    example_query = st.session_state.example_query
    del st.session_state.example_query
    
    if not st.session_state.agent:
        st.error("Please initialize the agent first using the sidebar.")
    else:
        # Add user message to chat
        st.session_state.chat_history.append({"role": "user", "content": example_query})
        
        # Process query
        with st.spinner("Processing your query..."):
            try:
                # Run async function
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(process_query(example_query, st.session_state.agent))
                loop.close()
                
                # Prepare response data
                response_data = {
                    "sql": result.get("generated_sql", ""),
                    "result": result.get("sql_result", ""),
                    "analysis": result.get("analysis", ""),
                    "retry_count": result.get("retry_count", 0),
                    "error": result.get("error_message", "")
                }
                
                # Add assistant response to chat
                st.session_state.chat_history.append({
                    "role": "assistant", 
                    "content": response_data
                })
                
                # Auto-save will happen automatically due to message count change
                
            except Exception as e:
                st.error(f"Error processing query: {str(e)}")
        
        # Rerun to update chat display
        st.rerun()

# Chat input
if prompt := st.chat_input("Ask a question about your database..."):
    if not st.session_state.agent:
        st.error("Please initialize the agent first using the sidebar.")
    else:
        # Add user message to chat
        st.session_state.chat_history.append({"role": "user", "content": prompt})
        
        # Process query
        with st.spinner("Processing your query..."):
            try:
                # Run async function
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(process_query(prompt, st.session_state.agent))
                loop.close()
                
                # Prepare response data
                response_data = {
                    "sql": result.get("generated_sql", ""),
                    "result": result.get("sql_result", ""),
                    "analysis": result.get("analysis", ""),
                    "retry_count": result.get("retry_count", 0),
                    "error": result.get("error_message", "")
                }
                
                # Add assistant response to chat
                st.session_state.chat_history.append({
                    "role": "assistant", 
                    "content": response_data
                })
                
                # Auto-save will happen automatically due to message count change
                
            except Exception as e:
                st.error(f"Error processing query: {str(e)}")
        
        # Rerun to update chat display
        st.rerun()

# Auto-save on page unload (JavaScript)
st.markdown("""
<script>
window.addEventListener('beforeunload', function (e) {
    // This will trigger Streamlit's built-in session state saving
    return undefined;
});
</script>
""", unsafe_allow_html=True)
